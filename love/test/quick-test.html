<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速测试 - 星空背景</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #000;
            color: white;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
        .success { background: rgba(0,255,0,0.2); }
        .error { background: rgba(255,0,0,0.2); }
        .video-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }
        .video-background video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <!-- 视频背景容器 -->
    <div class="video-background" id="videoBackground">
        <video id="testVideo" autoplay muted loop playsinline>
            <source src="" type="video/mp4">
        </video>
    </div>

    <h1>🚀 快速测试</h1>
    <div id="results"></div>
    <button onclick="runTest()">运行测试</button>
    <button onclick="testStarry()">测试星空背景</button>

    <script src="/src/client/scripts/video-loader.js"></script>
    <script>
        function addResult(message, isSuccess = true) {
            const div = document.createElement('div');
            div.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            div.textContent = message;
            document.getElementById('results').appendChild(div);
        }

        function runTest() {
            document.getElementById('results').innerHTML = '';
            
            // 测试1: 检查全局对象
            if (window.VideoLoader) {
                addResult('✅ window.VideoLoader 存在');
            } else {
                addResult('❌ window.VideoLoader 不存在', false);
            }
            
            if (window.videoLoader) {
                addResult('✅ window.videoLoader 存在');
            } else {
                addResult('❌ window.videoLoader 不存在', false);
            }
            
            // 测试2: 检查方法
            if (window.videoLoader && typeof window.videoLoader.init === 'function') {
                addResult('✅ videoLoader.init 方法存在');
            } else {
                addResult('❌ videoLoader.init 方法不存在', false);
            }
            
            if (window.videoLoader && typeof window.videoLoader.loadStarryBackground === 'function') {
                addResult('✅ videoLoader.loadStarryBackground 方法存在');
            } else {
                addResult('❌ videoLoader.loadStarryBackground 方法不存在', false);
            }
            
            // 测试3: 检查静态方法
            if (window.VideoLoader && typeof window.VideoLoader.integrateWithPage === 'function') {
                addResult('✅ VideoLoader.integrateWithPage 静态方法存在');
            } else {
                addResult('❌ VideoLoader.integrateWithPage 静态方法不存在', false);
            }
        }

        async function testStarry() {
            try {
                addResult('🌟 开始测试星空背景...');
                
                const videoElement = document.getElementById('testVideo');
                const success = await window.videoLoader.loadStarryBackground('home', videoElement);
                
                if (success) {
                    addResult('✅ 星空背景测试成功');
                } else {
                    addResult('❌ 星空背景测试失败', false);
                }
            } catch (error) {
                addResult(`❌ 星空背景测试异常: ${error.message}`, false);
            }
        }

        // 页面加载后自动运行测试
        window.addEventListener('load', () => {
            setTimeout(runTest, 1000);
        });
    </script>
</body>
</html>
