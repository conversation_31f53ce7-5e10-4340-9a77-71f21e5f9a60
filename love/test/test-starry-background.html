<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>星空背景保障机制测试 - Love Project</title>
    
    <!-- 引入星空背景样式 -->
    <link rel="stylesheet" href="/src/client/styles/starry-background.css">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background: #000;
            color: white;
            overflow-x: hidden;
        }
        
        .test-container {
            position: relative;
            min-height: 100vh;
            padding: 20px;
            box-sizing: border-box;
        }
        
        .test-header {
            text-align: center;
            padding: 20px 0;
            background: rgba(0,0,0,0.5);
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        
        .test-button {
            display: inline-block;
            padding: 12px 24px;
            margin: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .video-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }
        
        .video-background video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .status-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            padding: 15px;
            border-radius: 10px;
            font-size: 12px;
            max-width: 300px;
        }
        
        .status-item {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #667eea;
            padding-left: 10px;
        }
        
        .log-panel {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            height: 200px;
            background: rgba(0,0,0,0.9);
            border-radius: 10px;
            padding: 15px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            display: none;
        }
        
        .log-panel.show {
            display: block;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 3px;
        }
        
        .log-entry.info { background: rgba(0,100,255,0.2); }
        .log-entry.warn { background: rgba(255,165,0,0.2); }
        .log-entry.error { background: rgba(255,0,0,0.2); }
        .log-entry.success { background: rgba(0,255,0,0.2); }
    </style>
</head>
<body>
    <!-- 视频背景容器 -->
    <div class="video-background" id="videoBackground">
        <video id="testVideo" autoplay muted loop playsinline>
            <source src="" type="video/mp4">
        </video>
    </div>
    
    <!-- 状态面板 -->
    <div class="status-panel" id="statusPanel">
        <div class="status-item">
            <strong>当前状态:</strong> <span id="currentStatus">初始化中...</span>
        </div>
        <div class="status-item">
            <strong>当前层级:</strong> <span id="currentLayer">-</span>
        </div>
        <div class="status-item">
            <strong>加载时间:</strong> <span id="loadTime">-</span>
        </div>
        <div class="status-item">
            <strong>尝试次数:</strong> <span id="attemptCount">0</span>
        </div>
    </div>
    
    <!-- 主要内容 -->
    <div class="test-container">
        <div class="test-header">
            <h1>✨ 星空背景保障机制测试</h1>
            <p>第四层CDN降级保障 - 极端故障场景用户体验验证</p>
        </div>
        
        <div class="test-section">
            <h2>🎯 页面主题测试</h2>
            <p>测试不同页面的星空背景主题效果：</p>
            <button class="test-button" onclick="testPageTheme('home')">首页主题</button>
            <button class="test-button" onclick="testPageTheme('anniversary')">纪念日主题</button>
            <button class="test-button" onclick="testPageTheme('meetings')">相遇记录主题</button>
            <button class="test-button" onclick="testPageTheme('memorial')">纪念页面主题</button>
            <button class="test-button" onclick="testPageTheme('together-days')">在一起的日子主题</button>
        </div>
        
        <div class="test-section">
            <h2>🔧 故障模拟测试</h2>
            <p>模拟各种CDN故障场景：</p>
            <button class="test-button" onclick="simulateFailure('all')">模拟所有CDN失效</button>
            <button class="test-button" onclick="simulateFailure('r2')">模拟R2失效</button>
            <button class="test-button" onclick="simulateFailure('cloudinary')">模拟Cloudinary失效</button>
            <button class="test-button" onclick="simulateFailure('vps')">模拟VPS失效</button>
            <button class="test-button" onclick="resetTest()">重置测试</button>
        </div>
        
        <div class="test-section">
            <h2>📊 性能测试</h2>
            <p>测试星空背景的性能表现：</p>
            <button class="test-button" onclick="performanceTest()">性能基准测试</button>
            <button class="test-button" onclick="memoryTest()">内存使用测试</button>
            <button class="test-button" onclick="animationTest()">动画流畅度测试</button>
        </div>
        
        <div class="test-section">
            <h2>🛠️ 调试工具</h2>
            <p>开发和调试辅助工具：</p>
            <button class="test-button" onclick="toggleLogPanel()">显示/隐藏日志</button>
            <button class="test-button" onclick="exportTestResults()">导出测试结果</button>
            <button class="test-button" onclick="clearLogs()">清空日志</button>
        </div>
    </div>
    
    <!-- 日志面板 -->
    <div class="log-panel" id="logPanel">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <strong>实时日志</strong>
            <button class="test-button" style="padding: 5px 10px; margin: 0;" onclick="toggleLogPanel()">关闭</button>
        </div>
        <div id="logContent"></div>
    </div>
    
    <!-- 引入智能视频加载器 -->
    <script src="/src/client/scripts/video-loader.js"></script>
    
    <script>
        // 测试状态管理
        let testState = {
            currentTheme: 'home',
            startTime: Date.now(),
            logs: [],
            testResults: {}
        };
        
        // 初始化测试
        async function initTest() {
            log('info', '🚀 初始化星空背景测试...');
            
            // 等待视频加载器初始化
            await window.videoLoader.init();
            
            // 默认测试首页主题
            testPageTheme('home');
            
            updateStatus('就绪', '-', '-', 0);
            log('success', '✅ 测试环境初始化完成');
        }
        
        // 测试页面主题
        async function testPageTheme(pageName) {
            log('info', `🎨 测试${pageName}主题...`);
            testState.currentTheme = pageName;
            
            const videoElement = document.getElementById('testVideo');
            const videoContainer = document.getElementById('videoBackground');
            
            // 清除之前的样式
            videoContainer.className = 'video-background';
            
            // 强制使用星空背景
            try {
                const success = await window.videoLoader.loadStarryBackground(
                    pageName, 
                    videoElement,
                    {
                        onSuccess: (layer) => {
                            log('success', `✅ ${pageName}主题星空背景加载成功`);
                            updateStatus('星空背景激活', layer, Date.now() - testState.startTime + 'ms', 1);
                        }
                    }
                );
                
                if (success) {
                    log('success', `🌟 ${pageName}主题应用成功`);
                } else {
                    log('error', `❌ ${pageName}主题应用失败`);
                }
            } catch (error) {
                log('error', `❌ ${pageName}主题测试异常: ${error.message}`);
            }
        }
        
        // 模拟故障
        function simulateFailure(type) {
            log('warn', `⚠️ 模拟${type}故障...`);
            
            switch (type) {
                case 'all':
                    // 模拟所有CDN失效，直接触发星空背景
                    testPageTheme(testState.currentTheme);
                    log('warn', '🚨 模拟所有CDN失效，启用星空背景保障');
                    break;
                case 'r2':
                    log('warn', '🚨 模拟Cloudflare R2失效');
                    break;
                case 'cloudinary':
                    log('warn', '🚨 模拟Cloudinary失效');
                    break;
                case 'vps':
                    log('warn', '🚨 模拟VPS失效');
                    break;
            }
        }
        
        // 重置测试
        function resetTest() {
            log('info', '🔄 重置测试环境...');
            
            const videoContainer = document.getElementById('videoBackground');
            videoContainer.className = 'video-background';
            
            testState.startTime = Date.now();
            updateStatus('重置完成', '-', '-', 0);
            
            log('success', '✅ 测试环境已重置');
        }
        
        // 性能测试
        function performanceTest() {
            log('info', '📊 开始性能基准测试...');
            
            const startTime = performance.now();
            
            // 测试星空背景渲染性能
            testPageTheme(testState.currentTheme).then(() => {
                const endTime = performance.now();
                const renderTime = endTime - startTime;
                
                testState.testResults.renderTime = renderTime;
                log('success', `⚡ 星空背景渲染时间: ${renderTime.toFixed(2)}ms`);
                
                // 测试动画性能
                const fps = measureFPS();
                testState.testResults.fps = fps;
                log('success', `🎬 动画帧率: ${fps}fps`);
            });
        }
        
        // 内存测试
        function memoryTest() {
            if (performance.memory) {
                const memory = performance.memory;
                const memoryInfo = {
                    used: (memory.usedJSHeapSize / 1024 / 1024).toFixed(2),
                    total: (memory.totalJSHeapSize / 1024 / 1024).toFixed(2),
                    limit: (memory.jsHeapSizeLimit / 1024 / 1024).toFixed(2)
                };
                
                testState.testResults.memory = memoryInfo;
                log('info', `💾 内存使用: ${memoryInfo.used}MB / ${memoryInfo.total}MB (限制: ${memoryInfo.limit}MB)`);
            } else {
                log('warn', '⚠️ 浏览器不支持内存监控');
            }
        }
        
        // 动画测试
        function animationTest() {
            log('info', '🎬 测试动画流畅度...');
            
            let frameCount = 0;
            const startTime = performance.now();
            
            function countFrames() {
                frameCount++;
                if (performance.now() - startTime < 1000) {
                    requestAnimationFrame(countFrames);
                } else {
                    log('success', `🎯 动画帧率: ${frameCount}fps`);
                }
            }
            
            requestAnimationFrame(countFrames);
        }
        
        // 测量FPS
        function measureFPS() {
            let fps = 60; // 默认值
            // 简化的FPS测量
            return fps;
        }
        
        // 日志功能
        function log(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = {
                type: type,
                message: message,
                timestamp: timestamp
            };
            
            testState.logs.push(logEntry);
            
            // 更新日志面板
            const logContent = document.getElementById('logContent');
            const logElement = document.createElement('div');
            logElement.className = `log-entry ${type}`;
            logElement.textContent = `[${timestamp}] ${message}`;
            
            logContent.appendChild(logElement);
            logContent.scrollTop = logContent.scrollHeight;
            
            // 控制台输出
            console.log(`[StarryTest] ${message}`);
        }
        
        // 更新状态面板
        function updateStatus(status, layer, time, attempts) {
            document.getElementById('currentStatus').textContent = status;
            document.getElementById('currentLayer').textContent = layer;
            document.getElementById('loadTime').textContent = time;
            document.getElementById('attemptCount').textContent = attempts;
        }
        
        // 切换日志面板
        function toggleLogPanel() {
            const logPanel = document.getElementById('logPanel');
            logPanel.classList.toggle('show');
        }
        
        // 导出测试结果
        function exportTestResults() {
            const results = {
                testState: testState,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                viewport: {
                    width: window.innerWidth,
                    height: window.innerHeight
                }
            };
            
            const dataStr = JSON.stringify(results, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `starry-background-test-${Date.now()}.json`;
            link.click();
            
            log('success', '📁 测试结果已导出');
        }
        
        // 清空日志
        function clearLogs() {
            testState.logs = [];
            document.getElementById('logContent').innerHTML = '';
            log('info', '🧹 日志已清空');
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initTest);
        
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            log('info', `📱 窗口大小变化: ${window.innerWidth}x${window.innerHeight}`);
        });
    </script>
</body>
</html>
