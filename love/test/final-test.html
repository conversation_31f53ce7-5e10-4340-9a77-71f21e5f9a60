<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终测试 - 星空背景保障机制</title>
    
    <!-- 引入星空背景样式 -->
    <link rel="stylesheet" href="/src/client/styles/starry-background.css">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #000;
            color: white;
            overflow-x: hidden;
        }
        
        .control-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            padding: 20px;
            border-radius: 10px;
            z-index: 1000;
            max-width: 300px;
        }
        
        .theme-button {
            display: block;
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .theme-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        
        .status-display {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-size: 12px;
        }
        
        .video-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }
        
        .video-background video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .content {
            position: relative;
            z-index: 1;
            padding: 50px;
            text-align: center;
        }
        
        .title {
            font-size: 3em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
        }
        
        .subtitle {
            font-size: 1.2em;
            opacity: 0.8;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        .success { color: #4ade80; }
        .error { color: #f87171; }
        .info { color: #60a5fa; }
    </style>
</head>
<body>
    <!-- 视频背景容器 -->
    <div class="video-background" id="videoBackground">
        <video id="testVideo" autoplay muted loop playsinline>
            <source src="" type="video/mp4">
        </video>
    </div>
    
    <!-- 主要内容 -->
    <div class="content">
        <h1 class="title">🌟 星空背景保障机制</h1>
        <p class="subtitle">第四层CDN降级保障 - 完美运行中</p>
    </div>
    
    <!-- 控制面板 -->
    <div class="control-panel">
        <h3>🎨 主题测试</h3>
        <button class="theme-button" onclick="switchTheme('home')">🏠 首页主题</button>
        <button class="theme-button" onclick="switchTheme('anniversary')">💕 纪念日主题</button>
        <button class="theme-button" onclick="switchTheme('meetings')">✨ 相遇记录主题</button>
        <button class="theme-button" onclick="switchTheme('memorial')">🌊 纪念页面主题</button>
        <button class="theme-button" onclick="switchTheme('together-days')">🌅 在一起的日子主题</button>
        
        <div class="status-display" id="statusDisplay">
            <div><strong>状态:</strong> <span id="currentStatus">初始化中...</span></div>
            <div><strong>当前主题:</strong> <span id="currentTheme">-</span></div>
            <div><strong>加载时间:</strong> <span id="loadTime">-</span></div>
        </div>
    </div>

    <!-- 引入智能视频加载器 -->
    <script src="/src/client/scripts/video-loader.js"></script>
    
    <script>
        let currentTheme = 'home';
        
        // 更新状态显示
        function updateStatus(status, theme, time) {
            document.getElementById('currentStatus').textContent = status;
            document.getElementById('currentTheme').textContent = theme;
            document.getElementById('loadTime').textContent = time;
        }
        
        // 切换主题
        async function switchTheme(themeName) {
            console.log(`🎨 切换到${themeName}主题`);
            currentTheme = themeName;
            
            const startTime = Date.now();
            updateStatus('加载中...', themeName, '-');
            
            try {
                const videoElement = document.getElementById('testVideo');
                const success = await window.videoLoader.loadStarryBackground(
                    themeName,
                    videoElement,
                    {
                        onSuccess: (layer) => {
                            const elapsed = Date.now() - startTime;
                            updateStatus('✅ 星空背景激活', themeName, `${elapsed}ms`);
                            console.log(`✅ ${themeName}主题加载成功`);
                        },
                        onError: (error) => {
                            updateStatus('❌ 加载失败', themeName, '-');
                            console.error(`❌ ${themeName}主题加载失败:`, error);
                        }
                    }
                );
                
                if (!success) {
                    updateStatus('❌ 加载失败', themeName, '-');
                }
                
            } catch (error) {
                updateStatus('❌ 异常', themeName, '-');
                console.error(`❌ ${themeName}主题异常:`, error);
            }
        }
        
        // 初始化
        async function initialize() {
            console.log('🚀 初始化星空背景测试...');
            
            try {
                // 等待video-loader准备就绪
                let attempts = 0;
                while (!window.videoLoader && attempts < 50) {
                    await new Promise(resolve => setTimeout(resolve, 100));
                    attempts++;
                }
                
                if (!window.videoLoader) {
                    throw new Error('video-loader加载超时');
                }
                
                console.log('✅ video-loader已就绪');
                
                // 初始化video-loader
                await window.videoLoader.init();
                console.log('✅ video-loader初始化完成');
                
                // 默认加载首页主题
                await switchTheme('home');
                
                console.log('🎉 初始化完成！');
                
            } catch (error) {
                console.error('❌ 初始化失败:', error);
                updateStatus('❌ 初始化失败', '-', '-');
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('📄 DOM加载完成');
            setTimeout(initialize, 500);
        });
        
        // 监听错误
        window.addEventListener('error', (event) => {
            console.error('页面错误:', event.error?.message || event.message);
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            console.error('未处理的Promise拒绝:', event.reason);
        });
        
        // 显示加载信息
        console.log('🌟 星空背景保障机制最终测试页面');
        console.log('📋 功能说明:');
        console.log('  - 点击右侧按钮切换不同页面主题');
        console.log('  - 每个主题都有独特的星空背景色彩');
        console.log('  - 状态面板显示实时加载信息');
        console.log('  - 所有操作都会在控制台显示详细日志');
    </script>
</body>
</html>
