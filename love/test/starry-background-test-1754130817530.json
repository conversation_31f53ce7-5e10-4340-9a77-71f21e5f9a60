{"testState": {"currentTheme": "together-days", "startTime": 1754130793835, "logs": [{"type": "info", "message": "🚀 初始化星空背景测试...", "timestamp": "18:33:08"}, {"type": "info", "message": "🎨 测试home主题...", "timestamp": "18:33:09"}, {"type": "error", "message": "❌ home主题测试异常: Cannot read properties of undefined (reading 'loadStarryBackground')", "timestamp": "18:33:09"}, {"type": "info", "message": "🎨 测试anniversary主题...", "timestamp": "18:33:10"}, {"type": "error", "message": "❌ anniversary主题测试异常: Cannot read properties of undefined (reading 'loadStarryBackground')", "timestamp": "18:33:10"}, {"type": "info", "message": "🎨 测试meetings主题...", "timestamp": "18:33:10"}, {"type": "error", "message": "❌ meetings主题测试异常: Cannot read properties of undefined (reading 'loadStarryBackground')", "timestamp": "18:33:10"}, {"type": "info", "message": "🎨 测试memorial主题...", "timestamp": "18:33:10"}, {"type": "error", "message": "❌ memorial主题测试异常: Cannot read properties of undefined (reading 'loadStarryBackground')", "timestamp": "18:33:10"}, {"type": "info", "message": "🎨 测试together-days主题...", "timestamp": "18:33:11"}, {"type": "error", "message": "❌ together-days主题测试异常: Cannot read properties of undefined (reading 'loadStarryBackground')", "timestamp": "18:33:11"}, {"type": "warn", "message": "⚠️ 模拟all故障...", "timestamp": "18:33:12"}, {"type": "info", "message": "🎨 测试together-days主题...", "timestamp": "18:33:12"}, {"type": "error", "message": "❌ together-days主题测试异常: Cannot read properties of undefined (reading 'loadStarryBackground')", "timestamp": "18:33:12"}, {"type": "warn", "message": "🚨 模拟所有CDN失效，启用星空背景保障", "timestamp": "18:33:12"}, {"type": "warn", "message": "⚠️ 模拟r2故障...", "timestamp": "18:33:12"}, {"type": "warn", "message": "🚨 模拟Cloudflare R2失效", "timestamp": "18:33:12"}, {"type": "warn", "message": "⚠️ 模拟cloudinary故障...", "timestamp": "18:33:13"}, {"type": "warn", "message": "🚨 模拟Cloudinary失效", "timestamp": "18:33:13"}, {"type": "warn", "message": "⚠️ 模拟vps故障...", "timestamp": "18:33:13"}, {"type": "warn", "message": "🚨 模拟VPS失效", "timestamp": "18:33:13"}, {"type": "info", "message": "🔄 重置测试环境...", "timestamp": "18:33:13"}, {"type": "success", "message": "✅ 测试环境已重置", "timestamp": "18:33:13"}, {"type": "info", "message": "📊 开始性能基准测试...", "timestamp": "18:33:14"}, {"type": "info", "message": "🎨 测试together-days主题...", "timestamp": "18:33:14"}, {"type": "error", "message": "❌ together-days主题测试异常: Cannot read properties of undefined (reading 'loadStarryBackground')", "timestamp": "18:33:14"}, {"type": "success", "message": "⚡ 星空背景渲染时间: 0.10ms", "timestamp": "18:33:14"}, {"type": "success", "message": "🎬 动画帧率: 60fps", "timestamp": "18:33:14"}, {"type": "info", "message": "💾 内存使用: 38.84MB / 46.39MB (限制: 4095.75MB)", "timestamp": "18:33:15"}, {"type": "info", "message": "🎬 测试动画流畅度...", "timestamp": "18:33:15"}, {"type": "success", "message": "🎯 动画帧率: 257fps", "timestamp": "18:33:16"}], "testResults": {"renderTime": 0.09999996423721313, "fps": 60, "memory": {"used": "38.84", "total": "46.39", "limit": "4095.75"}}}, "timestamp": "2025-08-02T10:33:37.529Z", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0", "viewport": {"width": 2084, "height": 1058}}